test01/basicmathfunctionsf16.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\BasicMathFunctionsF16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_f16.c
