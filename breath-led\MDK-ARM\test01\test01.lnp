--cpu=Cortex-M4.fp.sp
"test01\startup_stm32f429xx.o"
"test01\main.o"
"test01\gpio.o"
"test01\stm32f4xx_it.o"
"test01\stm32f4xx_hal_msp.o"
"test01\stm32f4xx_hal_tim.o"
"test01\stm32f4xx_hal_tim_ex.o"
"test01\stm32f4xx_hal_rcc.o"
"test01\stm32f4xx_hal_rcc_ex.o"
"test01\stm32f4xx_hal_flash.o"
"test01\stm32f4xx_hal_flash_ex.o"
"test01\stm32f4xx_hal_flash_ramfunc.o"
"test01\stm32f4xx_hal_gpio.o"
"test01\stm32f4xx_hal_dma_ex.o"
"test01\stm32f4xx_hal_dma.o"
"test01\stm32f4xx_hal_pwr.o"
"test01\stm32f4xx_hal_pwr_ex.o"
"test01\stm32f4xx_hal_cortex.o"
"test01\stm32f4xx_hal.o"
"test01\stm32f4xx_hal_exti.o"
"test01\system_stm32f4xx.o"
"test01\scheduler.o"
"test01\led_app.o"
--strict --scatter "test01\test01.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "test01.map" -o test01\test01.axf