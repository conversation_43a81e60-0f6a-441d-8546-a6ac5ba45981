./output/fastmathfunctionsf16.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\FastMathFunctionsF16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vexp_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_vec_math_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_helium_utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vinverse_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_atan2_f16.c
