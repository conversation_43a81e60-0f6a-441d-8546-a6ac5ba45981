{"configurations": [{"name": "test01", "includePath": ["d:\\GD32_code\\ASK\\Core\\Inc", "d:\\GD32_code\\ASK\\Drivers\\STM32F4xx_HAL_Driver\\Inc", "d:\\GD32_code\\ASK\\Drivers\\STM32F4xx_HAL_Driver\\Inc\\Legacy", "d:\\GD32_code\\ASK\\Drivers\\CMSIS\\Device\\ST\\STM32F4xx\\Include", "d:\\GD32_code\\ASK\\Drivers\\CMSIS\\Include", "d:\\GD32_code\\ASK\\app", "d:\\GD32_code\\ASK\\Components\\ebtn", "d:\\GD32_code\\ASK\\Components\\ringbuffer", "d:\\GD32_code\\ASK\\MDK-ARM", "d:\\GD32_code\\ASK\\Core\\Src", "d:\\GD32_code\\ASK\\Drivers\\STM32F4xx_HAL_Driver\\Src"], "defines": ["USE_HAL_DRIVER", "STM32F429xx", "ARM_MATH_CM4", "__alignof__(x)=", "__asm(x)=", "__asm__(x)=", "__forceinline=", "__restrict=", "__volatile__=", "__inline=", "__inline__=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__unaligned=", "__promise(x)=", "__irq=", "__swi=", "__weak=", "__register=", "__pure=", "__value_in_regs=", "__breakpoint(x)=", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__enable_fiq()=", "__enable_irq()=", "__force_stores()=", "__memory_changed()=", "__schedule_barrier()=", "__semihost(x,y)=0", "__vfp_status(x,y)=0", "__builtin_arm_nop()=", "__builtin_arm_wfi()=", "__builtin_arm_wfe()=", "__builtin_arm_sev()=", "__builtin_arm_sevl()=", "__builtin_arm_yield()=", "__builtin_arm_isb(x)=", "__builtin_arm_dsb(x)=", "__builtin_arm_dmb(x)=", "__builtin_bswap32(x)=0U", "__builtin_bswap16(x)=0U", "__builtin_arm_rbit(x)=0U", "__builtin_clz(x)=0U", "__builtin_arm_ldrex(x)=0U", "__builtin_arm_strex(x,y)=0U", "__builtin_arm_clrex()=", "__builtin_arm_ssat(x,y)=0U", "__builtin_arm_usat(x,y)=0U", "__builtin_arm_ldaex(x)=0U", "__builtin_arm_stlex(x,y)=0U", "__GNUC__=4", "__GNUC_MINOR__=2", "__GNUC_PATCHLEVEL__=1"], "intelliSenseMode": "${default}"}], "version": 4}