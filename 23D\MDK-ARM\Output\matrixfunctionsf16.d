./output/matrixfunctionsf16.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\MatrixFunctionsF16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_add_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_sub_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_trans_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_scale_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_vec_mult_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_trans_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_mult_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_inverse_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_init_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cholesky_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_solve_upper_triangular_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_solve_lower_triangular_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_qr_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_householder_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h
