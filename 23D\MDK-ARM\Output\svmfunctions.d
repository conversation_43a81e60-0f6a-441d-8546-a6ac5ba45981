./output/svmfunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\SVMFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_linear_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_linear_predict_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_polynomial_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_polynomial_predict_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_rbf_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_rbf_predict_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_sigmoid_init_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_sigmoid_predict_f32.c
