#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_5
ADC1.DMAContinuousRequests=ENABLE
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T3_TRGO
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,master,DMAContinuousRequests,ScanConvMode,NbrOfConversion,ExternalTrigConv
ADC1.NbrOfConversion=1
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.ScanConvMode=DISABLE
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
DAC.DAC_Trigger=DAC_TRIGGER_T6_TRGO
DAC.IPParameters=DAC_Trigger
Dma.ADC1.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.1.Instance=DMA2_Stream0
Dma.ADC1.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.1.MemInc=DMA_MINC_ENABLE
Dma.ADC1.1.Mode=DMA_NORMAL
Dma.ADC1.1.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.1.Priority=DMA_PRIORITY_MEDIUM
Dma.ADC1.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.DAC1.2.Direction=DMA_MEMORY_TO_PERIPH
Dma.DAC1.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.DAC1.2.Instance=DMA1_Stream5
Dma.DAC1.2.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.DAC1.2.MemInc=DMA_MINC_ENABLE
Dma.DAC1.2.Mode=DMA_CIRCULAR
Dma.DAC1.2.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.DAC1.2.PeriphInc=DMA_PINC_DISABLE
Dma.DAC1.2.Priority=DMA_PRIORITY_LOW
Dma.DAC1.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=USART1_RX
Dma.Request1=ADC1
Dma.Request2=DAC1
Dma.Request3=USART3_RX
Dma.RequestsNb=4
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_HIGH
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.3.Instance=DMA1_Stream1
Dma.USART3_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.3.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.3.Mode=DMA_NORMAL
Dma.USART3_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.3.Priority=DMA_PRIORITY_HIGH
Dma.USART3_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F429ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DAC
Mcu.IP10=USART3
Mcu.IP2=DMA
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM3
Mcu.IP7=TIM6
Mcu.IP8=TIM14
Mcu.IP9=USART1
Mcu.IPNb=11
Mcu.Name=STM32F429Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PC14/OSC32_IN
Mcu.Pin1=PC15/OSC32_OUT
Mcu.Pin10=PE11
Mcu.Pin11=PE13
Mcu.Pin12=PE15
Mcu.Pin13=PB10
Mcu.Pin14=PB11
Mcu.Pin15=PD8
Mcu.Pin16=PD9
Mcu.Pin17=PD10
Mcu.Pin18=PD11
Mcu.Pin19=PD12
Mcu.Pin2=PH0/OSC_IN
Mcu.Pin20=PD13
Mcu.Pin21=PA8
Mcu.Pin22=PA9
Mcu.Pin23=PA10
Mcu.Pin24=PA11
Mcu.Pin25=PA12
Mcu.Pin26=PA13
Mcu.Pin27=PA14
Mcu.Pin28=PA15
Mcu.Pin29=PC10
Mcu.Pin3=PH1/OSC_OUT
Mcu.Pin30=PC11
Mcu.Pin31=PC12
Mcu.Pin32=PD1
Mcu.Pin33=PD2
Mcu.Pin34=PD3
Mcu.Pin35=PD4
Mcu.Pin36=PD5
Mcu.Pin37=VP_SYS_VS_Systick
Mcu.Pin38=VP_TIM3_VS_ClockSourceINT
Mcu.Pin39=VP_TIM6_VS_ClockSourceINT
Mcu.Pin4=PC0
Mcu.Pin40=VP_TIM14_VS_ClockSourceINT
Mcu.Pin5=PA4
Mcu.Pin6=PA5
Mcu.Pin7=PB0
Mcu.Pin8=PE7
Mcu.Pin9=PE9
Mcu.PinsNb=41
Mcu.ThirdParty0=STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0
Mcu.ThirdPartyNb=1
Mcu.UserConstants=
Mcu.UserName=STM32F429ZGTx
MxCube.Version=6.11.1
MxDb.Version=DB.6.0.111
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM8_TRG_COM_TIM14_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA11.GPIO_Label=AD9959_P1
PA11.GPIO_PuPd=GPIO_PULLUP
PA11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA11.Locked=true
PA11.Signal=GPIO_Output
PA12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA12.GPIO_Label=AD9959_P2
PA12.GPIO_PuPd=GPIO_PULLUP
PA12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA12.Locked=true
PA12.Signal=GPIO_Output
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA15.GPIO_Label=AD9959_P3
PA15.GPIO_PuPd=GPIO_PULLUP
PA15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA15.Locked=true
PA15.Signal=GPIO_Output
PA4.Signal=COMP_DAC1_group
PA5.Locked=true
PA5.Signal=ADCx_IN5
PA8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA8.GPIO_Label=AD9959_P0
PA8.GPIO_PuPd=GPIO_PULLUP
PA8.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.GPIOParameters=GPIO_PuPd
PB0.GPIO_PuPd=GPIO_PULLUP
PB0.Locked=true
PB0.Signal=GPIO_Input
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PC0.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC0.GPIO_Label=AD9959_PDC
PC0.GPIO_PuPd=GPIO_PULLUP
PC0.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC0.Locked=true
PC0.Signal=GPIO_Output
PC10.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC10.GPIO_Label=AD9959_SDIO0
PC10.GPIO_PuPd=GPIO_PULLUP
PC10.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC10.Locked=true
PC10.Signal=GPIO_Output
PC11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC11.GPIO_Label=AD9959_SDIO1
PC11.GPIO_PuPd=GPIO_PULLUP
PC11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC11.Locked=true
PC11.Signal=GPIO_Output
PC12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC12.GPIO_Label=AD9959_SDIO2
PC12.GPIO_PuPd=GPIO_PULLUP
PC12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC12.Locked=true
PC12.Signal=GPIO_Output
PC14/OSC32_IN.Mode=LSE-External-Oscillator
PC14/OSC32_IN.Signal=RCC_OSC32_IN
PC15/OSC32_OUT.Mode=LSE-External-Oscillator
PC15/OSC32_OUT.Signal=RCC_OSC32_OUT
PD1.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD1.GPIO_Label=AD9959_RST
PD1.GPIO_PuPd=GPIO_PULLUP
PD1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD1.Locked=true
PD1.Signal=GPIO_Output
PD10.Locked=true
PD10.Signal=GPIO_Output
PD11.Locked=true
PD11.Signal=GPIO_Output
PD12.Locked=true
PD12.Signal=GPIO_Output
PD13.Locked=true
PD13.Signal=GPIO_Output
PD2.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD2.GPIO_Label=AD9959_UP
PD2.GPIO_PuPd=GPIO_PULLUP
PD2.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD2.Locked=true
PD2.Signal=GPIO_Output
PD3.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD3.GPIO_Label=AD9959_CS
PD3.GPIO_PuPd=GPIO_PULLUP
PD3.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD3.Locked=true
PD3.Signal=GPIO_Output
PD4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD4.GPIO_Label=AD9959_SCK
PD4.GPIO_PuPd=GPIO_PULLUP
PD4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD4.Locked=true
PD4.Signal=GPIO_Output
PD5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD5.GPIO_Label=AD9959_SDIO3
PD5.GPIO_PuPd=GPIO_PULLUP
PD5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD5.Locked=true
PD5.Signal=GPIO_Output
PD8.Locked=true
PD8.Signal=GPIO_Output
PD9.Locked=true
PD9.Signal=GPIO_Output
PE11.GPIOParameters=GPIO_PuPd
PE11.GPIO_PuPd=GPIO_PULLUP
PE11.Locked=true
PE11.Signal=GPIO_Input
PE13.GPIOParameters=GPIO_PuPd
PE13.GPIO_PuPd=GPIO_PULLUP
PE13.Locked=true
PE13.Signal=GPIO_Input
PE15.GPIOParameters=GPIO_PuPd
PE15.GPIO_PuPd=GPIO_PULLUP
PE15.Locked=true
PE15.Signal=GPIO_Input
PE7.GPIOParameters=GPIO_PuPd
PE7.GPIO_PuPd=GPIO_PULLUP
PE7.Locked=true
PE7.Signal=GPIO_Input
PE9.GPIOParameters=GPIO_PuPd
PE9.GPIO_PuPd=GPIO_PULLUP
PE9.Locked=true
PE9.Signal=GPIO_Input
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F429ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=test01.ioc
ProjectManager.ProjectName=test01
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_ADC1_Init-ADC1-false-HAL-true,6-MX_TIM3_Init-TIM3-false-HAL-true,7-MX_DAC_Init-DAC-false-HAL-true,8-MX_TIM6_Init-TIM6-false-HAL-true,9-MX_TIM14_Init-TIM14-false-HAL-true,10-MX_USART3_UART_Init-USART3-false-HAL-true
RCC.48MHZClocksFreq_Value=90000000
RCC.AHBFreq_Value=180000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=45000000
RCC.APB1TimFreq_Value=90000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=90000000
RCC.APB2TimFreq_Value=180000000
RCC.CortexFreq_Value=180000000
RCC.EthernetFreq_Value=180000000
RCC.FCLKCortexFreq_Value=180000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=180000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=160000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LCDTFTFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VCOSAIOutputFreq_ValueR,VcooutputI2S,VcooutputI2SQ
RCC.LCDTFTFreq_Value=20416666.666666668
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=180000000
RCC.PLLCLKFreq_Value=180000000
RCC.PLLM=15
RCC.PLLN=216
RCC.PLLQCLKFreq_Value=90000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SAI_AClocksFreq_Value=20416666.666666668
RCC.SAI_BClocksFreq_Value=20416666.666666668
RCC.SYSCLKFreq_VALUE=180000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=320000000
RCC.VCOInputFreq_Value=1666666.6666666667
RCC.VCOOutputFreq_Value=360000000
RCC.VCOSAIOutputFreq_Value=81666666.66666667
RCC.VCOSAIOutputFreq_ValueQ=20416666.666666668
RCC.VCOSAIOutputFreq_ValueR=40833333.333333336
RCC.VcooutputI2S=160000000
RCC.VcooutputI2SQ=160000000
SH.ADCx_IN5.0=ADC1_IN5,IN5
SH.ADCx_IN5.ConfNb=1
SH.COMP_DAC1_group.0=DAC_OUT1,DAC_OUT1
SH.COMP_DAC1_group.ConfNb=1
STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0.DSPOoLibraryJjLibrary_Checked=false
STMicroelectronics.X-CUBE-ALGOBUILD.1.4.0_SwParameter=LibraryCcDSPOoLibraryJjDSPOoLibrary\:true;
TIM14.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM14.IPParameters=Prescaler,Period,AutoReloadPreload
TIM14.Period=5000-1
TIM14.Prescaler=180-1
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.IPParameters=Prescaler,Period,TIM_MasterOutputTrigger,AutoReloadPreload
TIM3.Period=100-1
TIM3.Prescaler=36-1
TIM3.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
TIM6.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM6.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM6.Period=100-1
TIM6.Prescaler=180-1
TIM6.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM14_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM14_VS_ClockSourceINT.Signal=TIM14_VS_ClockSourceINT
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
