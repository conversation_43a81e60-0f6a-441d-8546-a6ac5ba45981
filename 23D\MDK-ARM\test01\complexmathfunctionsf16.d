test01/complexmathfunctionsf16.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\ComplexMathFunctionsF16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_conj_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_dot_prod_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_real_f16.c
