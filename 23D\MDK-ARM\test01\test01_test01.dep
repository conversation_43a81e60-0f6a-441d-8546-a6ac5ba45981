Dependencies for Project 'test01', Target 'test01': (DO NOT MODIFY !)
CompilerVersion: 6220000::V6.22::ARMCLANG
F (startup_stm32f429xx.s)(0x6869E508)(--target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -masm=auto  -Wa,armasm,--diag_suppress=A1950W -c

-gdwarf-4

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-Wa,armasm,--pd,"__UVISION_VERSION SETA 541" -Wa,armasm,--pd,"GD32F470 SETA 1" -Wa,armasm,--pd,"_RTE_ SETA 1"

-o test01/startup_stm32f429xx.o)
F (../Core/Src/main.c)(0x6878AE9B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/main.o -MMD)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (..\Core\Inc\dma.h)(0x6832C43E)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Core\Inc\gpio.h)(0x681ECCC2)
I (..\app\mydefine.h)(0x6869E594)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\led_app.h)(0x682E8532)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\dac_app.h)(0x68613431)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
F (../Core/Src/gpio.c)(0x68751D88)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/gpio.o -MMD)
I (..\Core\Inc\gpio.h)(0x681ECCC2)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Core/Src/adc.c)(0x68691B9A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/adc.o -MMD)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Core/Src/dac.c)(0x6860F912)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/dac.o -MMD)
I (..\Core\Inc\dac.h)(0x6860F912)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Core/Src/dma.c)(0x6860F913)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/dma.o -MMD)
I (..\Core\Inc\dma.h)(0x6832C43E)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Core/Src/tim.c)(0x686DD844)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/tim.o -MMD)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Core/Src/usart.c)(0x6860F21C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/usart.o -MMD)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\app\mydefine.h)(0x6869E594)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\led_app.h)(0x682E8532)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\dac_app.h)(0x68613431)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
F (../Core/Src/stm32f4xx_it.c)(0x6861F7CF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_it.o -MMD)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_it.h)(0x6861F7CF)
F (../Core/Src/stm32f4xx_hal_msp.c)(0x681ECCC3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_msp.o -MMD)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_adc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_adc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_ll_adc.o -MMD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_rcc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_rcc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_flash.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_flash_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_flash_ramfunc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_gpio.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_dma_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_dma.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_pwr.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_pwr_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_cortex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_exti.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_dac.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dac_ex.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_dac_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_tim.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_tim_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c)(0x68611AD8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/stm32f4xx_hal_uart.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (../Core/Src/system_stm32f4xx.c)(0x68623925)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/system_stm32f4xx.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
F (..\Components\ebtn\bit_array.h)(0x65FAA0DD)()
F (..\Components\ebtn\ebtn.c)(0x65FAA0DD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/ebtn.o -MMD)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
F (..\Components\ebtn\ebtn.h)(0x65FAA0DD)()
F (..\Components\ringbuffer\ringbuffer.c)(0x67FB299A)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/ringbuffer.o -MMD)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
F (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)()
F (..\app\mydefine.h)(0x6869E594)()
F (..\app\scheduler.c)(0x68620B2F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/scheduler.o -MMD)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\mydefine.h)(0x6869E594)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\led_app.h)(0x682E8532)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\dac_app.h)(0x68613431)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
F (..\app\led_app.c)(0x6860F4A1)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/led_app.o -MMD)
I (..\app\led_app.h)(0x682E8532)
I (..\app\mydefine.h)(0x6869E594)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\dac_app.h)(0x68613431)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
F (..\app\key_app.c)(0x6861F6B3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/key_app.o -MMD)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\mydefine.h)(0x6869E594)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\led_app.h)(0x682E8532)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\dac_app.h)(0x68613431)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
F (..\app\btn_app.c)(0x68627243)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/btn_app.o -MMD)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\mydefine.h)(0x6869E594)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\led_app.h)(0x682E8532)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\dac_app.h)(0x68613431)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
I (..\Core\Inc\gpio.h)(0x681ECCC2)
F (..\app\usart_app.c)(0x6835C726)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/usart_app.o -MMD)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\mydefine.h)(0x6869E594)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\led_app.h)(0x682E8532)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\dac_app.h)(0x68613431)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
F (..\app\adc_app.c)(0x68767110)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/adc_app.o -MMD)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\mydefine.h)(0x6869E594)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\led_app.h)(0x682E8532)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\dac_app.h)(0x68613431)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
F (..\app\dac_app.c)(0x6863C94C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/dac_app.o -MMD)
I (..\app\dac_app.h)(0x68613431)
I (..\app\mydefine.h)(0x6869E594)
I (..\Core\Inc\main.h)(0x681ECCC3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x68611AD8)
I (..\Core\Inc\stm32f4xx_hal_conf.h)(0x6860F914)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x68611AD8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\core_cm4.h)(0x68611AD4)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x68611AD4)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_exti.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_adc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_adc_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dac_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_tim_ex.h)(0x68611AD8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_uart.h)(0x68611AD8)
I (..\Components\ebtn\ebtn.h)(0x65FAA0DD)
I (..\Components\ebtn\bit_array.h)(0x65FAA0DD)
I (..\Core\Inc\usart.h)(0x6832C43F)
I (..\Components\ringbuffer\ringbuffer.h)(0x67FB29A0)
I (..\Core\Inc\tim.h)(0x6861F7CE)
I (..\Core\Inc\adc.h)(0x6835C6EB)
I (..\Core\Inc\dac.h)(0x6860F912)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (..\app\scheduler.h)(0x67FE8109)
I (..\app\led_app.h)(0x682E8532)
I (..\app\key_app.h)(0x682FDDEF)
I (..\app\btn_app.h)(0x6861F8DA)
I (..\app\usart_app.h)(0x6832D347)
I (..\app\adc_app.h)(0x686D0FF3)
I (..\app\waveform_analyzer_app.h)(0x6815FA72)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/BasicMathFunctions/BasicMathFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/basicmathfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_and_u16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_and_u32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_and_u8.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_not_u16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_not_u32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_not_u8.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_or_u16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_or_u32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_or_u8.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_shift_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_shift_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_shift_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_xor_u16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_xor_u32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_xor_u8.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_q7.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/BasicMathFunctions/BasicMathFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/basicmathfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_abs_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_add_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_dot_prod_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_mult_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_negate_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_offset_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_scale_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_sub_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BasicMathFunctions\arm_clip_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/BayesFunctions/BayesFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/bayesfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BayesFunctions\arm_gaussian_naive_bayes_predict_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/BayesFunctions/BayesFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/bayesfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\BayesFunctions\arm_gaussian_naive_bayes_predict_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/CommonTables/CommonTables.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/commontables.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\CommonTables\arm_common_tables.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\CommonTables\arm_const_structs.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_const_structs.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\CommonTables\arm_mve_tables.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/CommonTables/CommonTablesF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/commontablesf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\CommonTables\arm_common_tables_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\CommonTables\arm_const_structs_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_const_structs_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\CommonTables\arm_mve_tables_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/ComplexMathFunctions/ComplexMathFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/complexmathfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_conj_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_conj_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_conj_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_dot_prod_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_dot_prod_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_dot_prod_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_fast_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_real_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_real_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_real_q31.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/ComplexMathFunctions/ComplexMathFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/complexmathfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_conj_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_dot_prod_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mag_squared_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ComplexMathFunctions\arm_cmplx_mult_real_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/ControllerFunctions/ControllerFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/controllerfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\arm_pid_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\arm_pid_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\arm_pid_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\arm_pid_reset_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\arm_pid_reset_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\arm_pid_reset_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\arm_sin_cos_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\ControllerFunctions\arm_sin_cos_q31.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/DistanceFunctions/DistanceFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/distancefunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_boolean_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_boolean_distance_template.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_braycurtis_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_canberra_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_chebyshev_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_chebyshev_distance_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cityblock_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cityblock_distance_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_correlation_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cosine_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cosine_distance_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_dice_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_euclidean_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_euclidean_distance_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_hamming_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_jaccard_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_jensenshannon_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_kulsinski_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_minkowski_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_rogerstanimoto_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_russellrao_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_sokalmichener_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_sokalsneath_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_yule_distance.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_dtw_distance_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_dtw_path_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_dtw_init_window_q7.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/DistanceFunctions/DistanceFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/distancefunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_braycurtis_distance_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_canberra_distance_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_chebyshev_distance_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cityblock_distance_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_correlation_distance_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cosine_distance_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_euclidean_distance_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_jensenshannon_distance_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_minkowski_distance_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/FastMathFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/fastmathfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_cos_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_cos_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_cos_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sin_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sin_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sin_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sqrt_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_sqrt_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vexp_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vexp_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_divide_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_divide_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_atan2_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_atan2_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_atan2_q15.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/FastMathFunctions/FastMathFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/fastmathfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vexp_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_vec_math_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_helium_utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vlog_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_vinverse_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FastMathFunctions\arm_atan2_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/FilteringFunctions/FilteringFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/filteringfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_32x64_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_32x64_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_fast_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_fast_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df2T_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df2T_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df2T_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df2T_init_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_fast_opt_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_fast_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_fast_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_opt_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_opt_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_fast_opt_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_fast_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_fast_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_opt_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_opt_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_partial_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_conv_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_fast_opt_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_fast_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_fast_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_opt_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_opt_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_fast_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_fast_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_init_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_decimate_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_fast_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_fast_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_init_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_init_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_interpolate_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_interpolate_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_interpolate_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_interpolate_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_interpolate_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_interpolate_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_lattice_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_lattice_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_lattice_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_lattice_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_lattice_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_lattice_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_sparse_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_sparse_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_sparse_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_sparse_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_sparse_init_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_sparse_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_sparse_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_sparse_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_iir_lattice_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_iir_lattice_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_iir_lattice_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_iir_lattice_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_iir_lattice_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_iir_lattice_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_norm_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_norm_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_norm_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_norm_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_norm_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_norm_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_lms_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_levinson_durbin_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_levinson_durbin_q31.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/FilteringFunctions/FilteringFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/filteringfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_fir_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df1_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df2T_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_df2T_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_correlate_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\FilteringFunctions\arm_levinson_durbin_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/InterpolationFunctions/InterpolationFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/interpolationfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_spline_interp_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_spline_interp_init_f32.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/InterpolationFunctions/InterpolationFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/interpolationfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_bilinear_interp_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\InterpolationFunctions\arm_linear_interp_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/MatrixFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/matrixfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_add_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_add_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_add_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_mult_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_mult_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_mult_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_init_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_inverse_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_inverse_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_fast_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_fast_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_opt_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_scale_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_scale_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_scale_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_sub_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_sub_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_sub_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_sub_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_trans_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_trans_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_trans_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_trans_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_trans_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_vec_mult_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_vec_mult_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_vec_mult_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_vec_mult_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_trans_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_trans_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_trans_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cholesky_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cholesky_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_solve_upper_triangular_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_solve_lower_triangular_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_solve_upper_triangular_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_solve_lower_triangular_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_ldlt_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_ldlt_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_qr_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_qr_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_householder_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_householder_f32.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/MatrixFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/matrixfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_add_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_sub_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_trans_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_scale_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_mult_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_vec_mult_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_trans_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cmplx_mult_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_inverse_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_cholesky_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_solve_upper_triangular_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_solve_lower_triangular_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_mat_qr_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\MatrixFunctions\arm_householder_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/QuaternionMathFunctions/QuaternionMathFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/quaternionmathfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_norm_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_inverse_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_conjugate_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_normalize_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_product_single_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion_product_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_quaternion2rotation_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\QuaternionMathFunctions\arm_rotation2quaternion_f32.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/SVMFunctions/SVMFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/svmfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_linear_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_linear_predict_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_polynomial_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_polynomial_predict_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_rbf_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_rbf_predict_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_sigmoid_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_sigmoid_predict_f32.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/SVMFunctions/SVMFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/svmfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_linear_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_linear_predict_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_polynomial_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_polynomial_predict_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_rbf_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_rbf_predict_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_sigmoid_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SVMFunctions\arm_svm_sigmoid_predict_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/StatisticsFunctions/StatisticsFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/statisticsfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_entropy_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_entropy_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_kullback_leibler_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_kullback_leibler_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_logsumexp_dot_prod_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_logsumexp_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_no_idx_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_no_idx_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_no_idx_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_no_idx_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_no_idx_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mean_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mean_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mean_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mean_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mean_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_no_idx_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_no_idx_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_no_idx_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_no_idx_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_no_idx_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_power_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_power_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_power_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_power_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_power_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_rms_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_rms_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_rms_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_std_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_std_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_std_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_std_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_var_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_var_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_var_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_var_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_no_idx_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_no_idx_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_no_idx_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_no_idx_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_no_idx_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_no_idx_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_no_idx_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_no_idx_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_no_idx_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_no_idx_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mse_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mse_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mse_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mse_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mse_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_accumulate_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_accumulate_f64.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/StatisticsFunctions/StatisticsFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/statisticsfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mean_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_power_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_rms_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_std_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_var_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_entropy_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_kullback_leibler_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_logsumexp_dot_prod_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_logsumexp_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_max_no_idx_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_min_no_idx_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmax_no_idx_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_absmin_no_idx_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_mse_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\StatisticsFunctions\arm_accumulate_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/SupportFunctions/SupportFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/supportfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_barycenter_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_bitonic_sort_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\PrivateInclude\arm_sorting.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\interpolation_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\bayes_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\controller_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\svm_defines.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\filtering_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\quaternion_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_bubble_sort_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_heap_sort_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_insertion_sort_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_merge_sort_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_merge_sort_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_quick_sort_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_selection_sort_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_sort_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_sort_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_weighted_average_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_float.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_float.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q31_to_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q31_to_float.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q31_to_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q31_to_q7.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q7_to_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q7_to_float.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q7_to_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q7_to_q31.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/SupportFunctions/SupportFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/supportfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f16_to_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f16_to_float.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f16_to_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_weighted_average_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_barycenter_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/TransformFunctions/TransformFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/transformfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_bitreversal.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_bitreversal2.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_const_structs.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix8_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_init_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_dct4_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_init_q31.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_init_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_init_q15.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_init_q31.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/TransformFunctions/TransformFunctionsF16.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/transformfunctionsf16.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\transform_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_const_structs_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_common_tables.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_rfft_fast_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix8_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_bitreversal_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_mfcc_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\complex_math_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions_f16.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix2_init_f16.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\TransformFunctions\arm_cfft_radix4_init_f16.c)(0x66A6B132)
F (D:/STM32/ARM/CMSIS-DSP/1.16.2/Source/WindowFunctions/WindowFunctions.c)(0x66A6B132)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Core/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/CMSIS/Include -I ../app -I ../Components/ebtn -I ../Components/ringbuffer

-I./RTE/_test01

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/Include

-ID:/STM32/ARM/CMSIS-DSP/1.16.2/PrivateInclude

-ID:/STM32/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-ID:/STM32/GigaDevice/GD32F4xx_DFP/3.0.3/Device/F4XX/Include

-D__UVISION_VERSION="541" -DGD32F470 -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4

-o test01/windowfunctions.o -MMD)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_welch_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\window_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h)(0x66A6B132)
I (..\Drivers\CMSIS\Include\cmsis_compiler.h)(0x68611AD4)
I (..\Drivers\CMSIS\Include\cmsis_armclang.h)(0x68611AD4)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_welch_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_bartlett_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_bartlett_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hamming_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hamming_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hanning_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hanning_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall3_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall3_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall4_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall4_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall3a_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall3a_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall3b_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall3b_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall4a_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall4a_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_blackman_harris_92db_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_blackman_harris_92db_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall4b_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall4b_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall4c_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_nuttall4c_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft90d_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft90d_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft95_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft95_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft116d_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft116d_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft144d_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft144d_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft169d_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft169d_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft196d_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft196d_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft223d_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft223d_f64.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft248d_f32.c)(0x66A6B132)
I (D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\WindowFunctions\arm_hft248d_f64.c)(0x66A6B132)
