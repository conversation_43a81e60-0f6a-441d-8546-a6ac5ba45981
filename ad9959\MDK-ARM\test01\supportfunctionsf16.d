test01/supportfunctionsf16.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\SupportFunctionsF16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_copy_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\support_functions_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types_f16.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_fill_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f16_to_q15.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f16_to_float.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f16_to_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_f64_to_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_q15_to_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_float_to_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_weighted_average_f16.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\SupportFunctions\arm_barycenter_f16.c
