--cpu=Cortex-M4.fp.sp
".\output\startup_stm32f429xx.o"
".\output\main.o"
".\output\gpio.o"
".\output\adc.o"
".\output\dac.o"
".\output\dma.o"
".\output\tim.o"
".\output\usart.o"
".\output\stm32f4xx_it.o"
".\output\stm32f4xx_hal_msp.o"
".\output\stm32f4xx_hal_adc.o"
".\output\stm32f4xx_hal_adc_ex.o"
".\output\stm32f4xx_ll_adc.o"
".\output\stm32f4xx_hal_rcc.o"
".\output\stm32f4xx_hal_rcc_ex.o"
".\output\stm32f4xx_hal_flash.o"
".\output\stm32f4xx_hal_flash_ex.o"
".\output\stm32f4xx_hal_flash_ramfunc.o"
".\output\stm32f4xx_hal_gpio.o"
".\output\stm32f4xx_hal_dma_ex.o"
".\output\stm32f4xx_hal_dma.o"
".\output\stm32f4xx_hal_pwr.o"
".\output\stm32f4xx_hal_pwr_ex.o"
".\output\stm32f4xx_hal_cortex.o"
".\output\stm32f4xx_hal.o"
".\output\stm32f4xx_hal_exti.o"
".\output\stm32f4xx_hal_dac.o"
".\output\stm32f4xx_hal_dac_ex.o"
".\output\stm32f4xx_hal_tim.o"
".\output\stm32f4xx_hal_tim_ex.o"
".\output\stm32f4xx_hal_uart.o"
".\output\system_stm32f4xx.o"
".\output\ebtn.o"
".\output\ringbuffer.o"
".\output\scheduler.o"
".\output\led_app.o"
".\output\key_app.o"
".\output\btn_app.o"
".\output\usart_app.o"
".\output\adc_app.o"
".\output\dac_app.o"
".\output\my_hmi.o"
".\output\ad9959.o"
".\output\basicmathfunctions.o"
".\output\basicmathfunctionsf16.o"
".\output\bayesfunctions.o"
".\output\bayesfunctionsf16.o"
".\output\commontables.o"
".\output\commontablesf16.o"
".\output\complexmathfunctions.o"
".\output\complexmathfunctionsf16.o"
".\output\controllerfunctions.o"
".\output\distancefunctions.o"
".\output\distancefunctionsf16.o"
".\output\fastmathfunctions.o"
".\output\fastmathfunctionsf16.o"
".\output\filteringfunctions.o"
".\output\filteringfunctionsf16.o"
".\output\interpolationfunctions.o"
".\output\interpolationfunctionsf16.o"
".\output\matrixfunctions.o"
".\output\matrixfunctionsf16.o"
".\output\quaternionmathfunctions.o"
".\output\svmfunctions.o"
".\output\svmfunctionsf16.o"
".\output\statisticsfunctions.o"
".\output\statisticsfunctionsf16.o"
".\output\supportfunctions.o"
".\output\supportfunctionsf16.o"
".\output\transformfunctions.o"
".\output\transformfunctionsf16.o"
".\output\windowfunctions.o"
--strict --scatter ".\Output\test01.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Output\test01.map" -o .\Output\test01.axf