#include "led_app.h"
uint8_t ucled[6]= {1, 0, 1, 0, 1, 0};;

void led_disp(uint8_t *ucled)
{
    uint8_t temp=0x00;
    static uint8_t temp_old=0xff;
    for(int i=0;i<6;i++)
    {
        if(ucled[i])
            temp|=(1<<i);
    }
    if(temp!=temp_old)
    {
        HAL_GPIO_WritePin(GPIOD,GPIO_PIN_8,(temp&0x01)?GPIO_PIN_SET:GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOD,GPIO_PIN_9,(temp&0x02)?GPIO_PIN_SET:GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOD,GPIO_PIN_10,(temp&0x04)?GPIO_PIN_SET:GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOD,GPIO_PIN_11,(temp&0x08)?GPIO_PIN_SET:GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOD,GPIO_PIN_12,(temp&0x10)?GPIO_PIN_SET:GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOD,GPIO_PIN_13,(temp&0x20)?GPIO_PIN_SET:GPIO_PIN_RESET);
        temp_old=temp;
    }
}

void led_task(void)
{
     /*ucled[0]=0; 
     ucled[1]=1; 
      ucled[2]=0; 
       ucled[3]=1; 
        ucled[4]=0; 
         ucled[5]=1; */
    led_disp(ucled);
}