test01/distancefunctions.o: \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\DistanceFunctions.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_boolean_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\distance_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h \
  ..\Drivers\CMSIS\Include\cmsis_compiler.h \
  ..\Drivers\CMSIS\Include\cmsis_armclang.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\none.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\statistics_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\basic_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\fast_math_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_functions.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_boolean_distance_template.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_braycurtis_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_canberra_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_chebyshev_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_chebyshev_distance_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cityblock_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cityblock_distance_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_correlation_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cosine_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_cosine_distance_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_dice_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_euclidean_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_euclidean_distance_f64.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_hamming_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_jaccard_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_jensenshannon_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_kulsinski_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_minkowski_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_rogerstanimoto_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_russellrao_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_sokalmichener_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_sokalsneath_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_yule_distance.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_dtw_distance_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Include\dsp\matrix_utils.h \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_dtw_path_f32.c \
  D:\STM32\ARM\CMSIS-DSP\1.16.2\Source\DistanceFunctions\arm_dtw_init_window_q7.c
